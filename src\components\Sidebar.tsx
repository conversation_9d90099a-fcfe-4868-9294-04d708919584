import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import {
  LogOut,
  BarChart3,
  QrCode,
  Settings,
  Home,
  TrendingUp,
  Users,
  Bell,
  HelpCircle,
  Globe,
  Shield
} from 'lucide-react';
import { Crisp } from 'crisp-sdk-web';

const navLinks = [
  { label: 'Dashboard', href: '/dashboard', icon: Home },
  { label: 'Custom Domains', href: '/dashboard/custom-domains', icon: Globe },
  // { label: 'QR Codes', href: '/dashboard/qr-codes', icon: QrCode },
  // { label: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },
  // { label: 'Performance', href: '/dashboard/performance', icon: TrendingUp },
  // { label: 'Users', href: '/dashboard/users', icon: Users },
  // { label: 'Notifications', href: '/dashboard/notifications', icon: Bell },
  // { label: 'Settings', href: '/dashboard/settings', icon: Settings },
];

const adminNavLinks = [
  { label: 'Team Management', href: '/admin/team', icon: Shield },
];

export default function Sidebar() {
  const { session, signOut } = useAuthStore();
  const user = session?.user;
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
  const [isAdmin, setIsAdmin] = useState(false);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user?.id) return;

      try {
        // We'll need to create an endpoint to check admin status
        // For now, we can check if user email matches admin criteria
        // This is a simplified check - in production you'd want a proper API endpoint
        const response = await fetch('/api/admin/check-status');
        const data = await response.json();
        setIsAdmin(data.isAdmin || false);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  const handleHelpClick = () => {
    if (typeof window !== 'undefined') {
      Crisp.chat.open();
    }
  };

  return (
    <div className="flex flex-col h-full relative overflow-hidden">
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent pointer-events-none" />

      {/* Brand */}
      <div className="px-8 py-6 border-b border-white/10 relative z-10">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-[#18BC9C] to-[#16A085] rounded-xl flex items-center justify-center shadow-lg">
            <QrCode className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold tracking-tight">QRAnalytica</h1>
            <p className="text-xs text-white/70">Analytics Dashboard</p>
          </div>
        </div>
      </div>

      {/* Nav */}
      <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto relative z-10">
        {navLinks.map((item) => {
          const Icon = item.icon;
          const isActive = currentPath === item.href ||
            (item.href === '/dashboard' && currentPath === '/dashboard/');

          return (
            <a
              key={item.href}
              href={item.href}
              className={`group flex items-center space-x-3 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-gradient-to-r from-[#18BC9C] to-[#16A085] text-white shadow-lg transform scale-105'
                  : 'text-white/80 hover:bg-white/10 hover:text-white hover:transform hover:scale-105'
              }`}
            >
              <Icon className={`h-5 w-5 transition-colors ${
                isActive ? 'text-white' : 'text-white/60 group-hover:text-white'
              }`} />
              <span className="font-medium">{item.label}</span>
              {isActive && (
                <div className="ml-auto w-2 h-2 bg-white rounded-full animate-pulse" />
              )}
            </a>
          );
        })}

        {/* Admin Section */}
        {isAdmin && adminNavLinks.length > 0 && (
          <>
            <div className="pt-4 pb-2">
              <div className="px-4 text-xs font-semibold text-white/50 uppercase tracking-wider">
                Administration
              </div>
            </div>
            {adminNavLinks.map((item) => {
              const Icon = item.icon;
              const isActive = currentPath === item.href;

              return (
                <a
                  key={item.href}
                  href={item.href}
                  className={`group flex items-center space-x-3 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg transform scale-105'
                      : 'text-white/80 hover:bg-white/10 hover:text-white hover:transform hover:scale-105'
                  }`}
                >
                  <Icon className={`h-5 w-5 transition-colors ${
                    isActive ? 'text-white' : 'text-white/60 group-hover:text-white'
                  }`} />
                  <span className="font-medium">{item.label}</span>
                  {isActive && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full animate-pulse" />
                  )}
                </a>
              );
            })}
          </>
        )}
      </nav>

      {/* Quick Actions */}
      <div className="px-4 py-4 border-t border-white/10 relative z-10">
        <div className="space-y-2">
          <button
            onClick={handleHelpClick}
            className="w-full flex items-center space-x-3 rounded-lg px-3 py-2 text-sm text-white/70 hover:bg-white/10 hover:text-white transition-colors"
          >
            <HelpCircle className="h-4 w-4" />
            <span>Help & Support</span>
          </button>
        </div>
      </div>

      {/* User & Sign-out */}
      <div className="px-6 py-4 border-t border-white/10 relative z-10">
        <div className="flex items-center justify-between">
          {user ? (
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-br from-[#18BC9C] to-[#16A085] rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                {user.name?.charAt(0) || 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{user.name || 'User'}</p>
                <p className="text-xs text-white/60 truncate">{user.email || '<EMAIL>'}</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                G
              </div>
              <div>
                <p className="text-sm font-medium text-white">Guest</p>
                <p className="text-xs text-white/60">Not signed in</p>
              </div>
            </div>
          )}

          <button
            onClick={signOut}
            className="ml-3 p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            title="Sign out"
          >
            <LogOut className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}