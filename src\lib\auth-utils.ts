import type { User } from '../stores/authStore';

/**
 * Extract user information from request cookies
 */
export function getUserFromRequest(request: Request): User | null {
  try {
    const cookieHeader = request.headers.get('cookie');
    console.log('Debug: Cookie header:', cookieHeader);

    if (!cookieHeader) {
      console.log('Debug: No cookie header found');
      return null;
    }

    const cookies = cookieHeader.split('; ');
    const userCookie = cookies.find(cookie => cookie.startsWith('user='));
    console.log('Debug: User cookie found:', !!userCookie);

    if (!userCookie) {
      console.log('Debug: No user cookie found in:', cookies);
      return null;
    }

    const cookieValue = userCookie.split('=').slice(1).join('='); // Handle = in cookie value
    if (!cookieValue) {
      console.log('Debug: Empty cookie value');
      return null;
    }

    console.log('Debug: Raw cookie value:', cookieValue);

    // The cookie is double-encoded, so we need to decode it twice
    let jsonStr: string;
    try {
      const firstDecode = decodeURIComponent(cookieValue);
      jsonStr = decodeURIComponent(firstDecode);
      console.log('Debug: Decoded JSON string:', jsonStr);
    } catch (decodeError) {
      // If decoding fails, try using the raw value
      jsonStr = cookieValue;
      console.log('Debug: Using raw cookie value due to decode error:', decodeError);
    }

    const user = JSON.parse(jsonStr) as User;
    console.log('Debug: Parsed user:', { email: user.email, name: user.name, id: user.id });
    return user;
  } catch (error) {
    console.error('Error extracting user from request:', error);
    return null;
  }
}

/**
 * Get user ID from request, with fallback to email if ID is not available
 */
export function getUserIdFromRequest(request: Request): string | null {
  const user = getUserFromRequest(request);
  if (!user) return null;
  
  // Use user.id if available, otherwise fall back to email as identifier
  return user.id || user.email || null;
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(request: Request): boolean {
  const user = getUserFromRequest(request);
  const isAuth = user !== null;
  console.log('Debug: Authentication check result:', isAuth);
  return isAuth;
}

/**
 * Development helper - allows bypassing authentication in development
 */
export function isDevelopmentMode(): boolean {
  return process.env.NODE_ENV === 'development' ||
         typeof process !== 'undefined' && process.env.ASTRO_DEV === 'true';
}

/**
 * Check if user has admin privileges
 */
export async function isAdmin(request: Request, db: D1Database): Promise<boolean> {
  try {
    const user = getUserFromRequest(request);
    if (!user || !user.id) return false;

    const adminUser = await db.prepare(
      'SELECT is_admin FROM users WHERE id = ?'
    ).bind(user.id).first();

    return adminUser?.is_admin === 1;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Get user permissions for a specific admin
 */
export async function getUserPermissions(
  request: Request,
  db: D1Database,
  adminId: string
): Promise<{ hasAccess: boolean; permissionLevel?: string }> {
  try {
    const user = getUserFromRequest(request);
    if (!user || !user.id) return { hasAccess: false };

    // Check if user is the admin themselves
    if (user.id === adminId) {
      return { hasAccess: true, permissionLevel: 'DELETE' };
    }

    // Check if user is a team member with permissions
    const teamMember = await db.prepare(`
      SELECT permission_level
      FROM team_members
      WHERE admin_id = ? AND member_id = ? AND status = 'active'
    `).bind(adminId, user.id).first();

    if (teamMember) {
      return {
        hasAccess: true,
        permissionLevel: teamMember.permission_level
      };
    }

    return { hasAccess: false };
  } catch (error) {
    console.error('Error checking user permissions:', error);
    return { hasAccess: false };
  }
}

/**
 * Check if user can perform specific action based on permission level
 */
export function canPerformAction(
  userPermission: string,
  requiredPermission: 'VIEW' | 'EDIT' | 'DELETE'
): boolean {
  const permissionHierarchy = {
    'VIEW': 1,
    'EDIT': 2,
    'DELETE': 3
  };

  const userLevel = permissionHierarchy[userPermission as keyof typeof permissionHierarchy] || 0;
  const requiredLevel = permissionHierarchy[requiredPermission];

  return userLevel >= requiredLevel;
}

/**
 * Admin middleware - ensures user has admin privileges
 */
export async function requireAdmin(request: Request, db: D1Database): Promise<{ success: boolean; error?: string }> {
  const isUserAdmin = await isAdmin(request, db);

  if (!isUserAdmin) {
    return {
      success: false,
      error: 'Admin privileges required'
    };
  }

  return { success: true };
}
